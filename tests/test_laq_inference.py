#!/usr/bin/env python3
"""
LAQ推理测试脚本
测试预处理后的数据是否能够被LAQ模型正确处理
"""

import sys
import os
import torch
import json
from PIL import Image
from torchvision import transforms as T
import numpy as np

# 添加LAQ模块路径
sys.path.append('/home/<USER>/johnny_ws/lapa_ws/LAPA/laq')

from laq_model.latent_action_quantization import LatentActionQuantization

class LAQInferenceTester:
    def __init__(self, model_config=None):
        """
        LAQ推理测试器
        
        Args:
            model_config: 模型配置字典
        """
        # 默认模型配置（基于inference_sthv2.py）
        self.model_config = model_config or {
            'dim': 1024,
            'quant_dim': 32,
            'codebook_size': 1024,
            'image_size': 256,
            'patch_size': 32,
            'spatial_depth': 6,
            'temporal_depth': 6,
            'dim_head': 64,
            'heads': 16,
            'code_seq_len': 1,
        }
        
        # 图像变换（与inference_sthv2.py保持一致）
        self.transform = T.Compose([
            T.Lambda(lambda img: img.convert('RGB') if img.mode != 'RGB' else img),
            T.Resize((256, 256)),
            T.ToTensor()
        ])
        
        self.model = None
    
    def create_model(self):
        """创建LAQ模型"""
        try:
            self.model = LatentActionQuantization(**self.model_config)

            # 如果CUDA可用，将模型移到GPU
            if torch.cuda.is_available():
                self.model = self.model.cuda()
                print("✓ 模型已移至CUDA设备")

            print("✓ LAQ模型创建成功")
            print(f"  - 图像大小: {self.model_config['image_size']}")
            print(f"  - 补丁大小: {self.model_config['patch_size']}")
            print(f"  - 码本大小: {self.model_config['codebook_size']}")
            return True
        except Exception as e:
            print(f"✗ LAQ模型创建失败: {e}")
            return False
    
    def load_frame_pair(self, first_frame_path, second_frame_path):
        """
        加载帧对并转换为LAQ期望的格式
        返回形状为 [1, 3, 2, 256, 256] 的张量
        """
        try:
            # 加载图像
            img1 = Image.open(first_frame_path)
            img2 = Image.open(second_frame_path)
            
            # 应用变换
            tensor1 = self.transform(img1).unsqueeze(1)  # [3, 1, 256, 256]
            tensor2 = self.transform(img2).unsqueeze(1)  # [3, 1, 256, 256]
            
            # 拼接并添加batch维度
            combined = torch.cat([tensor1, tensor2], dim=1)  # [3, 2, 256, 256]
            batched = combined.unsqueeze(0)  # [1, 3, 2, 256, 256]
            
            return batched
        except Exception as e:
            print(f"✗ 加载帧对失败: {e}")
            return None
    
    def test_single_inference(self, frame_tensor):
        """测试单个帧对的推理"""
        if self.model is None:
            print("✗ 模型未创建")
            return False

        try:
            # 确保输入张量在正确的设备上
            if torch.cuda.is_available():
                frame_tensor = frame_tensor.cuda()

            with torch.no_grad():
                # 测试模型前向传播
                output = self.model(frame_tensor, return_only_codebook_ids=True)

                print(f"✓ 推理成功")
                print(f"  - 输入形状: {frame_tensor.shape}")
                print(f"  - 输出形状: {output.shape}")
                print(f"  - 输出类型: {output.dtype}")
                print(f"  - 码本索引范围: {output.min().item()} - {output.max().item()}")

                return True
        except Exception as e:
            print(f"✗ 推理失败: {e}")
            return False
    
    def test_batch_inference(self, frame_tensors):
        """测试批量推理"""
        if self.model is None:
            print("✗ 模型未创建")
            return False

        try:
            # 创建批量张量
            batch_tensor = torch.cat(frame_tensors, dim=0)

            # 确保批量张量在正确的设备上
            if torch.cuda.is_available():
                batch_tensor = batch_tensor.cuda()

            with torch.no_grad():
                output = self.model(batch_tensor, return_only_codebook_ids=True)

                print(f"✓ 批量推理成功")
                print(f"  - 批量大小: {batch_tensor.shape[0]}")
                print(f"  - 输入形状: {batch_tensor.shape}")
                print(f"  - 输出形状: {output.shape}")

                return True
        except Exception as e:
            print(f"✗ 批量推理失败: {e}")
            return False
    
    def test_preprocessed_data(self, data_dir, max_samples=5):
        """测试预处理后的数据"""
        print(f"\n=== 测试预处理数据 ===")
        
        # 检查数据目录
        if not os.path.exists(data_dir):
            print(f"✗ 数据目录不存在: {data_dir}")
            return False
        
        # 加载帧对信息
        frame_pairs_path = os.path.join(data_dir, 'frame_pairs.json')
        if not os.path.exists(frame_pairs_path):
            print(f"✗ 帧对文件不存在: {frame_pairs_path}")
            return False
        
        with open(frame_pairs_path, 'r', encoding='utf-8') as f:
            frame_pairs = json.load(f)
        
        print(f"✓ 找到 {len(frame_pairs)} 个帧对")
        
        # 创建模型
        if not self.create_model():
            return False
        
        # 测试单个推理
        success_count = 0
        test_tensors = []
        
        for i, pair in enumerate(frame_pairs[:max_samples]):
            print(f"\n--- 测试帧对 {i+1}/{min(max_samples, len(frame_pairs))} ---")
            
            # 加载帧对
            frame_tensor = self.load_frame_pair(pair['first_frame'], pair['second_frame'])
            if frame_tensor is None:
                continue
            
            # 测试推理
            if self.test_single_inference(frame_tensor):
                success_count += 1
                test_tensors.append(frame_tensor)
        
        print(f"\n单个推理测试结果: {success_count}/{max_samples} 成功")
        
        # 测试批量推理
        if len(test_tensors) > 1:
            print(f"\n--- 测试批量推理 ---")
            self.test_batch_inference(test_tensors[:3])  # 测试前3个
        
        return success_count > 0
    
    def test_jsonl_format(self, jsonl_path):
        """测试JSONL格式数据"""
        print(f"\n=== 测试JSONL格式 ===")
        
        if not os.path.exists(jsonl_path):
            print(f"✗ JSONL文件不存在: {jsonl_path}")
            return False
        
        try:
            with open(jsonl_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"✓ JSONL文件包含 {len(lines)} 条记录")
            
            # 检查前几条记录的格式
            valid_records = 0
            for i, line in enumerate(lines[:5]):
                try:
                    record = json.loads(line.strip())
                    required_fields = ['id', 'image', 'instruction']
                    
                    if all(field in record for field in required_fields):
                        # 检查图像文件是否存在
                        if os.path.exists(record['image']):
                            valid_records += 1
                            if i == 0:  # 显示第一条记录的详细信息
                                print(f"  示例记录:")
                                print(f"    ID: {record['id']}")
                                print(f"    图像: {os.path.basename(record['image'])}")
                                print(f"    指令: {record['instruction'][:50]}...")
                        else:
                            print(f"  ⚠ 记录 {i+1} 的图像文件不存在: {record['image']}")
                    else:
                        print(f"  ⚠ 记录 {i+1} 缺少必需字段")
                        
                except json.JSONDecodeError as e:
                    print(f"  ✗ 记录 {i+1} JSON格式错误: {e}")
            
            print(f"✓ 前5条记录验证: {valid_records}/5 有效")
            return valid_records > 0
            
        except Exception as e:
            print(f"✗ 读取JSONL文件失败: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='LAQ推理测试')
    parser.add_argument('--data_dir', type=str, required=True, help='预处理数据目录')
    parser.add_argument('--max_samples', type=int, default=5, help='最大测试样本数')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = LAQInferenceTester()
    
    # 测试预处理数据
    success = tester.test_preprocessed_data(args.data_dir, args.max_samples)
    
    # 测试JSONL格式
    jsonl_path = os.path.join(args.data_dir, 'laq_dataset.jsonl')
    jsonl_success = tester.test_jsonl_format(jsonl_path)
    
    print(f"\n{'='*50}")
    print("LAQ推理测试结果:")
    print(f"  预处理数据测试: {'✓ 通过' if success else '✗ 失败'}")
    print(f"  JSONL格式测试: {'✓ 通过' if jsonl_success else '✗ 失败'}")
    
    if success and jsonl_success:
        print("\n🎉 所有测试通过！数据已准备好用于LAQ推理。")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查数据预处理。")
        return 1

if __name__ == "__main__":
    exit(main())
