LAQ推理日志 - 20250801_135452
==================================================
命令: python /home/<USER>/johnny_ws/lapa_ws/LAPA/laq/inference_sthv2.py --input_file /home/<USER>/johnny_ws/lapa_ws/data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl --laq_checkpoint /home/<USER>/johnny_ws/lapa_ws/models/laq_openx.pt --unshuffled_jsonl /home/<USER>/johnny_ws/lapa_ws/laq_inference_results/laq_results_20250801_135452.jsonl --codebook_size 1024 --code_seq_len 1 --layer 6 --window_size 16 --batch_size 16 --dist_number 1
模型: /home/<USER>/johnny_ws/lapa_ws/models/laq_openx.pt
输入: /home/<USER>/johnny_ws/lapa_ws/data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl (1644 帧对)
输出: /home/<USER>/johnny_ws/lapa_ws/laq_inference_results/laq_results_20250801_135452.jsonl
==================================================

usage: inference_sthv2.py [-h] --input_file INPUT_FILE --dist_number
                          DIST_NUMBER --codebook_size CODEBOOK_SIZE
                          --laq_checkpoint LAQ_CHECKPOINT [--divider DIVIDER]
                          --window_size WINDOW_SIZE --code_seq_len
                          CODE_SEQ_LEN --layer LAYER --unshuffled_jsonl
                          UNSHUFFLED_JSONL
inference_sthv2.py: error: unrecognized arguments: --batch_size 16
